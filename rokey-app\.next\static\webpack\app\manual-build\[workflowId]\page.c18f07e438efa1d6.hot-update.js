"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/manual-build/[workflowId]/page",{

/***/ "(app-pages-browser)/./src/components/manual-build/nodes/BrowsingNode.tsx":
/*!************************************************************!*\
  !*** ./src/components/manual-build/nodes/BrowsingNode.tsx ***!
  \************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BrowsingNode)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_GlobeAltIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=GlobeAltIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js\");\n/* harmony import */ var _xyflow_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @xyflow/react */ \"(app-pages-browser)/./node_modules/@xyflow/react/dist/esm/index.js\");\n/* harmony import */ var _BaseNode__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./BaseNode */ \"(app-pages-browser)/./src/components/manual-build/nodes/BaseNode.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst providerColors = {\n    openai: '#10b981',\n    anthropic: '#f97316',\n    google: '#3b82f6',\n    deepseek: '#8b5cf6',\n    xai: '#374151',\n    openrouter: 'linear-gradient(45deg, #ff6b35, #f7931e, #3b82f6, #8b5cf6)'\n};\nconst providerNames = {\n    openai: 'OpenAI',\n    anthropic: 'Anthropic',\n    google: 'Google',\n    deepseek: 'DeepSeek',\n    xai: 'xAI (Grok)',\n    openrouter: 'OpenRouter'\n};\nfunction BrowsingNode(param) {\n    let { data, id } = param;\n    _s();\n    const edges = (0,_xyflow_react__WEBPACK_IMPORTED_MODULE_2__.useEdges)();\n    const nodes = (0,_xyflow_react__WEBPACK_IMPORTED_MODULE_2__.useNodes)();\n    const config = data.config;\n    const providerId = config === null || config === void 0 ? void 0 : config.providerId;\n    const modelId = config === null || config === void 0 ? void 0 : config.modelId;\n    const color = providerId ? providerColors[providerId] : '#10b981'; // Green default for browsing\n    const providerName = providerId ? providerNames[providerId] : 'Browsing AI';\n    // Get browsing capabilities\n    const getCapabilities = ()=>{\n        const capabilities = [];\n        if (config === null || config === void 0 ? void 0 : config.enableScreenshots) capabilities.push('📸 Screenshots');\n        if (config === null || config === void 0 ? void 0 : config.enableFormFilling) capabilities.push('📝 Forms');\n        if (config === null || config === void 0 ? void 0 : config.enableCaptchaSolving) capabilities.push('🔐 CAPTCHAs');\n        return capabilities;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BaseNode__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        data: data,\n        icon: _barrel_optimize_names_GlobeAltIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        color: \"#10b981\",\n        hasInput: false,\n        hasOutput: true,\n        inputHandles: [\n            {\n                id: 'plan',\n                label: 'Plan',\n                position: 'left'\n            },\n            {\n                id: 'memory',\n                label: 'Memory',\n                position: 'left'\n            }\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium text-white\",\n                                    children: \"Intelligent Browsing\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-green-400\",\n                                    children: \"● Ready\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                            lineNumber: 63,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-400\",\n                            children: [\n                                \"Max sites: \",\n                                maxSites,\n                                \" | Timeout: \",\n                                timeout,\n                                \"s\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-400\",\n                            children: [\n                                \"Engines: \",\n                                searchEngines.join(', ')\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, this),\n                        getCapabilities().length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-400\",\n                            children: getCapabilities().join(' • ')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 13\n                        }, this),\n                        (config === null || config === void 0 ? void 0 : config.maxDepth) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-400\",\n                            children: [\n                                \"Max depth: \",\n                                config.maxDepth,\n                                \" levels\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-xs text-green-300 bg-green-900/30 px-2 py-1 rounded\",\n                    children: \"\\uD83C\\uDF10 Autonomous Agent\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-xs text-gray-500\",\n                    children: \"Requires: Planner + Memory inputs\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                    lineNumber: 97,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n            lineNumber: 61,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, this);\n}\n_s(BrowsingNode, \"7n2V2B8JYzIze2JRCGcVoXmKUeo=\", false, function() {\n    return [\n        _xyflow_react__WEBPACK_IMPORTED_MODULE_2__.useEdges,\n        _xyflow_react__WEBPACK_IMPORTED_MODULE_2__.useNodes\n    ];\n});\n_c = BrowsingNode;\nvar _c;\n$RefreshReg$(_c, \"BrowsingNode\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/manual-build/nodes/BrowsingNode.tsx\n"));

/***/ })

});