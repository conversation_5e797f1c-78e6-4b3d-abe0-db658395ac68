"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/manual-build/[workflowId]/page",{

/***/ "(app-pages-browser)/./src/components/manual-build/nodes/BrowsingNode.tsx":
/*!************************************************************!*\
  !*** ./src/components/manual-build/nodes/BrowsingNode.tsx ***!
  \************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BrowsingNode)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_GlobeAltIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=GlobeAltIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js\");\n/* harmony import */ var _xyflow_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @xyflow/react */ \"(app-pages-browser)/./node_modules/@xyflow/react/dist/esm/index.js\");\n/* harmony import */ var _BaseNode__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./BaseNode */ \"(app-pages-browser)/./src/components/manual-build/nodes/BaseNode.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst providerColors = {\n    openai: '#10b981',\n    anthropic: '#f97316',\n    google: '#3b82f6',\n    deepseek: '#8b5cf6',\n    xai: '#374151',\n    openrouter: 'linear-gradient(45deg, #ff6b35, #f7931e, #3b82f6, #8b5cf6)'\n};\nconst providerNames = {\n    openai: 'OpenAI',\n    anthropic: 'Anthropic',\n    google: 'Google',\n    deepseek: 'DeepSeek',\n    xai: 'xAI (Grok)',\n    openrouter: 'OpenRouter'\n};\nfunction BrowsingNode(param) {\n    let { data, id } = param;\n    _s();\n    const edges = (0,_xyflow_react__WEBPACK_IMPORTED_MODULE_2__.useEdges)();\n    const nodes = (0,_xyflow_react__WEBPACK_IMPORTED_MODULE_2__.useNodes)();\n    const config = data.config;\n    const providerId = config === null || config === void 0 ? void 0 : config.providerId;\n    const modelId = config === null || config === void 0 ? void 0 : config.modelId;\n    const color = providerId ? providerColors[providerId] : '#10b981'; // Green default for browsing\n    const providerName = providerId ? providerNames[providerId] : 'Browsing AI';\n    // Get browsing capabilities\n    const getCapabilities = ()=>{\n        const capabilities = [];\n        if (config === null || config === void 0 ? void 0 : config.enableScreenshots) capabilities.push('📸 Screenshots');\n        if (config === null || config === void 0 ? void 0 : config.enableFormFilling) capabilities.push('📝 Forms');\n        if (config === null || config === void 0 ? void 0 : config.enableCaptchaSolving) capabilities.push('🔐 CAPTCHAs');\n        return capabilities;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BaseNode__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        data: data,\n        icon: _barrel_optimize_names_GlobeAltIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        color: typeof color === 'string' ? color : '#10b981',\n        hasInput: false,\n        hasOutput: true,\n        inputHandles: [\n            {\n                id: 'plan',\n                label: 'Plan',\n                position: 'left'\n            },\n            {\n                id: 'memory',\n                label: 'Memory',\n                position: 'left'\n            }\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-3\",\n            children: providerId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium text-white\",\n                                children: providerName\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs bg-gradient-to-r from-green-500 to-blue-500 text-white px-2 py-0.5 rounded-full\",\n                                children: \"Browsing\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 13\n                    }, this),\n                    modelId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-300 bg-gray-700/50 px-2 py-1 rounded\",\n                        children: [\n                            \"Model: \",\n                            modelId\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 15\n                    }, this),\n                    (config === null || config === void 0 ? void 0 : config.parameters) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 gap-2 text-xs\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-400\",\n                                children: [\n                                    \"Temp: \",\n                                    config.parameters.temperature || 1.0\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-400\",\n                                children: [\n                                    \"Max: \",\n                                    config.parameters.maxTokens || 'Auto'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 15\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-400\",\n                        children: [\n                            \"Max sites: \",\n                            (config === null || config === void 0 ? void 0 : config.maxSites) || 5,\n                            \" | Timeout: \",\n                            (config === null || config === void 0 ? void 0 : config.timeout) || 30,\n                            \"s\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 13\n                    }, this),\n                    (config === null || config === void 0 ? void 0 : config.searchEngines) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-400\",\n                        children: [\n                            \"Engines: \",\n                            config.searchEngines.join(', ')\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 15\n                    }, this),\n                    getCapabilities().length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-400\",\n                        children: getCapabilities().join(' • ')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 15\n                    }, this),\n                    (config === null || config === void 0 ? void 0 : config.maxDepth) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-400\",\n                        children: [\n                            \"Max depth: \",\n                            config.maxDepth,\n                            \" levels\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 15\n                    }, this),\n                    (config === null || config === void 0 ? void 0 : config.fallbackProvider) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded\",\n                        children: [\n                            \"Fallback: \",\n                            providerNames[config.fallbackProvider.providerId]\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 15\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                lineNumber: 63,\n                columnNumber: 11\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-gray-300\",\n                        children: \"Browsing AI Connection\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-400\",\n                        children: \"Configure to connect to AI models for intelligent web browsing and automation.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded\",\n                        children: \"⚠️ Needs configuration\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                lineNumber: 120,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n            lineNumber: 61,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, this);\n}\n_s(BrowsingNode, \"7n2V2B8JYzIze2JRCGcVoXmKUeo=\", false, function() {\n    return [\n        _xyflow_react__WEBPACK_IMPORTED_MODULE_2__.useEdges,\n        _xyflow_react__WEBPACK_IMPORTED_MODULE_2__.useNodes\n    ];\n});\n_c = BrowsingNode;\nvar _c;\n$RefreshReg$(_c, \"BrowsingNode\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/manual-build/nodes/BrowsingNode.tsx\n"));

/***/ })

});