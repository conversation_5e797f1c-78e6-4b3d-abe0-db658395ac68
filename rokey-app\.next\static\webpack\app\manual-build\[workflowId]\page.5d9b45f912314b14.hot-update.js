"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/manual-build/[workflowId]/page",{

/***/ "(app-pages-browser)/./src/components/manual-build/nodes/BrowsingNode.tsx":
/*!************************************************************!*\
  !*** ./src/components/manual-build/nodes/BrowsingNode.tsx ***!
  \************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BrowsingNode)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_GlobeAltIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=GlobeAltIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js\");\n/* harmony import */ var _xyflow_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @xyflow/react */ \"(app-pages-browser)/./node_modules/@xyflow/react/dist/esm/index.js\");\n/* harmony import */ var _BaseNode__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./BaseNode */ \"(app-pages-browser)/./src/components/manual-build/nodes/BaseNode.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst providerColors = {\n    openai: '#10b981',\n    anthropic: '#f97316',\n    google: '#3b82f6',\n    deepseek: '#8b5cf6',\n    xai: '#374151',\n    openrouter: 'linear-gradient(45deg, #ff6b35, #f7931e, #3b82f6, #8b5cf6)'\n};\nconst providerNames = {\n    openai: 'OpenAI',\n    anthropic: 'Anthropic',\n    google: 'Google',\n    deepseek: 'DeepSeek',\n    xai: 'xAI (Grok)',\n    openrouter: 'OpenRouter'\n};\nfunction BrowsingNode(param) {\n    let { data, id } = param;\n    _s();\n    const edges = (0,_xyflow_react__WEBPACK_IMPORTED_MODULE_2__.useEdges)();\n    const nodes = (0,_xyflow_react__WEBPACK_IMPORTED_MODULE_2__.useNodes)();\n    const config = data.config;\n    const providerId = config === null || config === void 0 ? void 0 : config.providerId;\n    const modelId = config === null || config === void 0 ? void 0 : config.modelId;\n    const color = providerId ? providerColors[providerId] : '#10b981'; // Green default for browsing\n    const providerName = providerId ? providerNames[providerId] : 'Browsing AI';\n    const maxSites = (config === null || config === void 0 ? void 0 : config.maxSites) || 5;\n    const timeout = (config === null || config === void 0 ? void 0 : config.timeout) || 30;\n    var _config_enableScreenshots;\n    const enableScreenshots = (_config_enableScreenshots = config === null || config === void 0 ? void 0 : config.enableScreenshots) !== null && _config_enableScreenshots !== void 0 ? _config_enableScreenshots : true;\n    var _config_enableFormFilling;\n    const enableFormFilling = (_config_enableFormFilling = config === null || config === void 0 ? void 0 : config.enableFormFilling) !== null && _config_enableFormFilling !== void 0 ? _config_enableFormFilling : true;\n    const searchEngines = (config === null || config === void 0 ? void 0 : config.searchEngines) || [\n        'google'\n    ];\n    const getCapabilities = ()=>{\n        const capabilities = [];\n        if (enableScreenshots) capabilities.push('📸 Screenshots');\n        if (enableFormFilling) capabilities.push('📝 Forms');\n        if (config === null || config === void 0 ? void 0 : config.enableCaptchaSolving) capabilities.push('🔐 CAPTCHAs');\n        return capabilities;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BaseNode__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        data: data,\n        icon: _barrel_optimize_names_GlobeAltIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        color: \"#10b981\",\n        hasInput: false,\n        hasOutput: true,\n        inputHandles: [\n            {\n                id: 'plan',\n                label: 'Plan',\n                position: 'left'\n            },\n            {\n                id: 'memory',\n                label: 'Memory',\n                position: 'left'\n            }\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium text-white\",\n                                    children: \"Intelligent Browsing\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-green-400\",\n                                    children: \"● Ready\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-400\",\n                            children: [\n                                \"Max sites: \",\n                                maxSites,\n                                \" | Timeout: \",\n                                timeout,\n                                \"s\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-400\",\n                            children: [\n                                \"Engines: \",\n                                searchEngines.join(', ')\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, this),\n                        getCapabilities().length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-400\",\n                            children: getCapabilities().join(' • ')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 13\n                        }, this),\n                        (config === null || config === void 0 ? void 0 : config.maxDepth) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-400\",\n                            children: [\n                                \"Max depth: \",\n                                config.maxDepth,\n                                \" levels\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-xs text-green-300 bg-green-900/30 px-2 py-1 rounded\",\n                    children: \"\\uD83C\\uDF10 Autonomous Agent\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                    lineNumber: 98,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-xs text-gray-500\",\n                    children: \"Requires: Planner + Memory inputs\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n            lineNumber: 66,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, this);\n}\n_s(BrowsingNode, \"7n2V2B8JYzIze2JRCGcVoXmKUeo=\", false, function() {\n    return [\n        _xyflow_react__WEBPACK_IMPORTED_MODULE_2__.useEdges,\n        _xyflow_react__WEBPACK_IMPORTED_MODULE_2__.useNodes\n    ];\n});\n_c = BrowsingNode;\nvar _c;\n$RefreshReg$(_c, \"BrowsingNode\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/manual-build/nodes/BrowsingNode.tsx\n"));

/***/ })

});