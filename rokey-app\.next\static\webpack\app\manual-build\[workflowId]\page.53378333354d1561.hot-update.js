"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/manual-build/[workflowId]/page",{

/***/ "(app-pages-browser)/./src/components/manual-build/nodes/BrowsingNode.tsx":
/*!************************************************************!*\
  !*** ./src/components/manual-build/nodes/BrowsingNode.tsx ***!
  \************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BrowsingNode)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_GlobeAltIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=GlobeAltIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js\");\n/* harmony import */ var _xyflow_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @xyflow/react */ \"(app-pages-browser)/./node_modules/@xyflow/react/dist/esm/index.js\");\n/* harmony import */ var _BaseNode__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./BaseNode */ \"(app-pages-browser)/./src/components/manual-build/nodes/BaseNode.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst providerColors = {\n    openai: '#10b981',\n    anthropic: '#f97316',\n    google: '#3b82f6',\n    deepseek: '#8b5cf6',\n    xai: '#374151',\n    openrouter: 'linear-gradient(45deg, #ff6b35, #f7931e, #3b82f6, #8b5cf6)'\n};\nconst providerNames = {\n    openai: 'OpenAI',\n    anthropic: 'Anthropic',\n    google: 'Google',\n    deepseek: 'DeepSeek',\n    xai: 'xAI (Grok)',\n    openrouter: 'OpenRouter'\n};\nfunction BrowsingNode(param) {\n    let { data, id } = param;\n    _s();\n    const edges = (0,_xyflow_react__WEBPACK_IMPORTED_MODULE_2__.useEdges)();\n    const nodes = (0,_xyflow_react__WEBPACK_IMPORTED_MODULE_2__.useNodes)();\n    const config = data.config;\n    const providerId = config === null || config === void 0 ? void 0 : config.providerId;\n    const modelId = config === null || config === void 0 ? void 0 : config.modelId;\n    const color = providerId ? providerColors[providerId] : '#10b981'; // Green default for browsing\n    const providerName = providerId ? providerNames[providerId] : 'Browsing AI';\n    const maxSites = (config === null || config === void 0 ? void 0 : config.maxSites) || 5;\n    const timeout = (config === null || config === void 0 ? void 0 : config.timeout) || 30;\n    var _config_enableScreenshots;\n    const enableScreenshots = (_config_enableScreenshots = config === null || config === void 0 ? void 0 : config.enableScreenshots) !== null && _config_enableScreenshots !== void 0 ? _config_enableScreenshots : true;\n    var _config_enableFormFilling;\n    const enableFormFilling = (_config_enableFormFilling = config === null || config === void 0 ? void 0 : config.enableFormFilling) !== null && _config_enableFormFilling !== void 0 ? _config_enableFormFilling : true;\n    const searchEngines = (config === null || config === void 0 ? void 0 : config.searchEngines) || [\n        'google'\n    ];\n    const getCapabilities = ()=>{\n        const capabilities = [];\n        if (enableScreenshots) capabilities.push('📸 Screenshots');\n        if (enableFormFilling) capabilities.push('📝 Forms');\n        if (config === null || config === void 0 ? void 0 : config.enableCaptchaSolving) capabilities.push('🔐 CAPTCHAs');\n        return capabilities;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BaseNode__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        data: data,\n        icon: _barrel_optimize_names_GlobeAltIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        color: typeof color === 'string' ? color : '#10b981',\n        hasInput: false,\n        hasOutput: true,\n        inputHandles: [\n            {\n                id: 'plan',\n                label: 'Plan',\n                position: 'left'\n            },\n            {\n                id: 'memory',\n                label: 'Memory',\n                position: 'left'\n            }\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-3\",\n            children: [\n                providerId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium text-white\",\n                                    children: providerName\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                                    lineNumber: 70,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs bg-gradient-to-r from-green-500 to-blue-500 text-white px-2 py-0.5 rounded-full\",\n                                    children: \"Browsing\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                                    lineNumber: 73,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 13\n                        }, this),\n                        modelId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-300 bg-gray-700/50 px-2 py-1 rounded\",\n                            children: [\n                                \"Model: \",\n                                modelId\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-400\",\n                            children: [\n                                \"Max sites: \",\n                                maxSites,\n                                \" | Timeout: \",\n                                timeout,\n                                \"s\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-400\",\n                            children: [\n                                \"Engines: \",\n                                searchEngines.join(', ')\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 13\n                        }, this),\n                        getCapabilities().length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-400\",\n                            children: getCapabilities().join(' • ')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 15\n                        }, this),\n                        (config === null || config === void 0 ? void 0 : config.maxDepth) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-400\",\n                            children: [\n                                \"Max depth: \",\n                                config.maxDepth,\n                                \" levels\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium text-white\",\n                                    children: \"Browsing Agent\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-yellow-400\",\n                                    children: \"⚠️ Not Configured\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-400\",\n                            children: \"Configure AI provider for browsing\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                    lineNumber: 105,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-xs text-green-300 bg-green-900/30 px-2 py-1 rounded\",\n                    children: \"\\uD83C\\uDF10 Autonomous Agent\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                    lineNumber: 121,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-xs text-gray-500\",\n                    children: \"Requires: Planner + Memory inputs\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n                    lineNumber: 125,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n            lineNumber: 66,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BrowsingNode.tsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, this);\n}\n_s(BrowsingNode, \"7n2V2B8JYzIze2JRCGcVoXmKUeo=\", false, function() {\n    return [\n        _xyflow_react__WEBPACK_IMPORTED_MODULE_2__.useEdges,\n        _xyflow_react__WEBPACK_IMPORTED_MODULE_2__.useNodes\n    ];\n});\n_c = BrowsingNode;\nvar _c;\n$RefreshReg$(_c, \"BrowsingNode\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/manual-build/nodes/BrowsingNode.tsx\n"));

/***/ })

});