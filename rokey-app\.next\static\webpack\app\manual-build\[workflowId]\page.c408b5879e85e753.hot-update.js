"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/manual-build/[workflowId]/page",{

/***/ "(app-pages-browser)/./src/components/manual-build/NodePalette.tsx":
/*!*****************************************************!*\
  !*** ./src/components/manual-build/NodePalette.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NodePalette)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentListIcon,CloudIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,GlobeAltIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UserIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentListIcon,CloudIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,GlobeAltIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentListIcon,CloudIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,GlobeAltIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentListIcon,CloudIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,GlobeAltIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CloudIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentListIcon,CloudIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,GlobeAltIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentListIcon,CloudIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,GlobeAltIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UserGroupIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentListIcon,CloudIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,GlobeAltIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClipboardDocumentListIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentListIcon,CloudIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,GlobeAltIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/WrenchScrewdriverIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentListIcon,CloudIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,GlobeAltIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CircleStackIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentListIcon,CloudIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,GlobeAltIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentListIcon,CloudIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,GlobeAltIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentListIcon,CloudIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,GlobeAltIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronRightIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst nodeCategories = {\n    core: {\n        label: 'Core Nodes',\n        description: 'Essential workflow components',\n        nodes: [\n            {\n                type: 'userRequest',\n                label: 'User Request',\n                description: 'Starting point for user input',\n                icon: _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n                category: 'core',\n                isAvailable: true,\n                defaultData: {\n                    label: 'User Request',\n                    config: {},\n                    isConfigured: true\n                }\n            },\n            {\n                type: 'classifier',\n                label: 'Classifier',\n                description: 'Analyzes and categorizes requests',\n                icon: _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n                category: 'core',\n                isAvailable: true,\n                defaultData: {\n                    label: 'Classifier',\n                    config: {},\n                    isConfigured: true\n                }\n            },\n            {\n                type: 'output',\n                label: 'Output',\n                description: 'Final response to user',\n                icon: _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n                category: 'core',\n                isAvailable: true,\n                defaultData: {\n                    label: 'Output',\n                    config: {},\n                    isConfigured: true\n                }\n            }\n        ]\n    },\n    ai: {\n        label: 'AI Providers',\n        description: 'AI model integrations',\n        nodes: [\n            {\n                type: 'provider',\n                label: 'AI Provider',\n                description: 'Connect to AI models (OpenAI, Claude, etc.)',\n                icon: _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                category: 'ai',\n                isAvailable: true,\n                defaultData: {\n                    label: 'AI Provider',\n                    config: {\n                        providerId: '',\n                        modelId: '',\n                        apiKey: '',\n                        parameters: {\n                            temperature: 1.0,\n                            maxTokens: undefined,\n                            topP: undefined,\n                            frequencyPenalty: undefined,\n                            presencePenalty: undefined\n                        }\n                    },\n                    isConfigured: false\n                }\n            },\n            {\n                type: 'vision',\n                label: 'Vision AI',\n                description: 'Multimodal AI for image analysis and vision tasks',\n                icon: _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                category: 'ai',\n                isAvailable: true,\n                defaultData: {\n                    label: 'Vision AI',\n                    config: {\n                        providerId: '',\n                        modelId: '',\n                        apiKey: '',\n                        parameters: {\n                            temperature: 1.0,\n                            maxTokens: undefined,\n                            topP: undefined,\n                            frequencyPenalty: undefined,\n                            presencePenalty: undefined\n                        }\n                    },\n                    isConfigured: false\n                }\n            },\n            {\n                type: 'roleAgent',\n                label: 'Role Agent',\n                description: 'Role plugin for AI providers (connect to role input)',\n                icon: _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                category: 'ai',\n                isAvailable: true,\n                defaultData: {\n                    label: 'Role Agent',\n                    config: {\n                        roleId: '',\n                        roleName: '',\n                        roleType: 'predefined',\n                        customPrompt: '',\n                        memoryEnabled: false\n                    },\n                    isConfigured: false\n                }\n            },\n            {\n                type: 'centralRouter',\n                label: 'Central Router',\n                description: 'Smart routing hub for multiple AI providers and vision models',\n                icon: _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n                category: 'ai',\n                isAvailable: true,\n                defaultData: {\n                    label: 'Central Router',\n                    config: {\n                        routingStrategy: 'smart',\n                        fallbackProvider: '',\n                        maxRetries: 3,\n                        timeout: 30000,\n                        enableCaching: true,\n                        debugMode: false\n                    },\n                    isConfigured: true\n                }\n            },\n            {\n                type: 'planner',\n                label: 'Planner',\n                description: 'AI model that creates browsing strategies and todo lists',\n                icon: _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                category: 'ai',\n                isAvailable: true,\n                defaultData: {\n                    label: 'Planner',\n                    config: {\n                        providerId: '',\n                        modelId: '',\n                        apiKey: '',\n                        parameters: {\n                            temperature: 0.7,\n                            maxTokens: 1000\n                        },\n                        maxSubtasks: 10\n                    },\n                    isConfigured: false\n                }\n            }\n        ]\n    },\n    tools: {\n        label: 'Tools & Integrations',\n        description: 'External service integrations',\n        nodes: [\n            {\n                type: 'tool',\n                label: 'Tools',\n                description: 'External tool integrations (Google Drive, Zapier, etc.)',\n                icon: _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                category: 'tools',\n                isAvailable: true,\n                defaultData: {\n                    label: 'Tools',\n                    config: {\n                        toolType: '',\n                        toolConfig: {},\n                        timeout: 30,\n                        connectionStatus: 'disconnected',\n                        isAuthenticated: false\n                    },\n                    isConfigured: false\n                }\n            },\n            {\n                type: 'memory',\n                label: 'Memory',\n                description: 'Store and retrieve data across workflow executions',\n                icon: _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                category: 'advanced',\n                isAvailable: true,\n                defaultData: {\n                    label: 'Memory',\n                    config: {\n                        memoryName: '',\n                        maxSize: 10240,\n                        encryption: true,\n                        description: ''\n                    },\n                    isConfigured: false\n                }\n            }\n        ]\n    },\n    browsing: {\n        label: 'Web Browsing',\n        description: 'Intelligent web browsing and automation',\n        nodes: [\n            {\n                type: 'browsing',\n                label: 'Browsing Agent',\n                description: 'Intelligent web browsing agent with multi-step automation',\n                icon: _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                category: 'advanced',\n                isAvailable: true,\n                defaultData: {\n                    label: 'Browsing Agent',\n                    config: {\n                        // AI Provider Configuration\n                        providerId: '',\n                        modelId: '',\n                        parameters: {\n                            temperature: 0.7,\n                            maxTokens: 1000,\n                            topP: 1.0,\n                            frequencyPenalty: 0,\n                            presencePenalty: 0\n                        },\n                        // Browsing Configuration\n                        maxSites: 5,\n                        timeout: 30,\n                        enableScreenshots: true,\n                        enableFormFilling: true,\n                        enableCaptchaSolving: false,\n                        searchEngines: [\n                            'google'\n                        ],\n                        maxDepth: 2,\n                        respectRobots: true,\n                        enableJavaScript: true\n                    },\n                    isConfigured: false\n                }\n            }\n        ]\n    }\n};\nfunction NodeItem(param) {\n    let { node, onAddNode } = param;\n    const Icon = node.icon;\n    const handleDragStart = (event)=>{\n        event.dataTransfer.setData('application/reactflow', node.type);\n        event.dataTransfer.effectAllowed = 'move';\n    };\n    const handleClick = ()=>{\n        // Add node at center of canvas\n        onAddNode(node.type);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        draggable: true,\n        onDragStart: handleDragStart,\n        onClick: handleClick,\n        className: \"p-3 rounded-lg border cursor-pointer transition-all duration-200 \".concat(node.isAvailable ? 'bg-gray-800/50 border-gray-700/50 hover:border-[#ff6b35]/50 hover:bg-gray-700/50' : 'bg-gray-900/50 border-gray-800/50 opacity-50 cursor-not-allowed'),\n        title: node.description,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center gap-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-2 rounded-lg \".concat(node.isAvailable ? 'bg-[#ff6b35]/20 text-[#ff6b35]' : 'bg-gray-700/50 text-gray-500'),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 297,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                    lineNumber: 294,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 min-w-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"font-medium text-sm \".concat(node.isAvailable ? 'text-white' : 'text-gray-500'),\n                            children: node.label\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                            lineNumber: 300,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-400 truncate\",\n                            children: node.description\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                            lineNumber: 305,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                    lineNumber: 299,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n            lineNumber: 293,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n        lineNumber: 282,\n        columnNumber: 5\n    }, this);\n}\n_c = NodeItem;\nfunction CategorySection(param) {\n    let { category, data, isExpanded, onToggle, onAddNode } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mb-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: onToggle,\n                className: \"w-full flex items-center justify-between p-3 bg-gray-800/30 hover:bg-gray-800/50 rounded-lg transition-colors\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"w-4 h-4 text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                                lineNumber: 335,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                className: \"w-4 h-4 text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                                lineNumber: 337,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-medium text-white\",\n                                children: data.label\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                                lineNumber: 339,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 333,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-xs text-gray-400\",\n                        children: data.nodes.length\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 341,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                lineNumber: 329,\n                columnNumber: 7\n            }, this),\n            isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-2 space-y-2\",\n                children: data.nodes.map((node)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NodeItem, {\n                        node: node,\n                        onAddNode: onAddNode\n                    }, node.type, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 347,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                lineNumber: 345,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n        lineNumber: 328,\n        columnNumber: 5\n    }, this);\n}\n_c1 = CategorySection;\nfunction NodePalette(param) {\n    let { onAddNode } = param;\n    _s();\n    const [expandedCategories, setExpandedCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set([\n        'core',\n        'ai'\n    ]) // Expand core and AI categories by default\n    );\n    const toggleCategory = (category)=>{\n        const newExpanded = new Set(expandedCategories);\n        if (newExpanded.has(category)) {\n            newExpanded.delete(category);\n        } else {\n            newExpanded.add(category);\n        }\n        setExpandedCategories(newExpanded);\n    };\n    const handleAddNode = (nodeType)=>{\n        // Add node at a default position (center of canvas)\n        onAddNode(nodeType, {\n            x: 400,\n            y: 200\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-80 bg-gray-900/80 backdrop-blur-sm border-r border-gray-700/50 p-4 overflow-y-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold text-white mb-2\",\n                        children: \"Node Palette\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 382,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-400\",\n                        children: \"Drag nodes to the canvas or click to add at center\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 383,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                lineNumber: 381,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-1\",\n                children: Object.entries(nodeCategories).map((param)=>{\n                    let [category, data] = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CategorySection, {\n                        category: category,\n                        data: data,\n                        isExpanded: expandedCategories.has(category),\n                        onToggle: ()=>toggleCategory(category),\n                        onAddNode: handleAddNode\n                    }, category, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 390,\n                        columnNumber: 11\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                lineNumber: 388,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 p-3 bg-blue-900/20 border border-blue-700/30 rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-blue-300 font-medium mb-1\",\n                        children: \"\\uD83D\\uDCA1 Pro Tip\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 402,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-blue-200\",\n                        children: \"Connect nodes by dragging from output handles to input handles. Every workflow must end with an Output node.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 403,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                lineNumber: 401,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n        lineNumber: 380,\n        columnNumber: 5\n    }, this);\n}\n_s(NodePalette, \"kKRKUKeIglQBeO0mlMXPYOz8OQo=\");\n_c2 = NodePalette;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"NodeItem\");\n$RefreshReg$(_c1, \"CategorySection\");\n$RefreshReg$(_c2, \"NodePalette\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/manual-build/NodePalette.tsx\n"));

/***/ })

});